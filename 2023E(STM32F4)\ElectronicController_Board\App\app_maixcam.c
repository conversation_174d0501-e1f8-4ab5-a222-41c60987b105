// Copyright (c) 2024 智能电子工程师

#include "app_maixcam.h"

// 默认回调函数
static void default_laser_callback(LaserCoord_t coord)
{

    // 打印激光坐标信息
    if (coord.type == RED_LASER_ID)
    {
        // 第一次读取坐标值
        app_pid_init();
        // 设置默认目标位置
        app_pid_set_target(100, 100);
        // 打印坐标值
        my_printf(&huart1, "R: X=%d, Y=%d\r\n", coord.x, coord.y);
        // 启动PID
        app_pid_start();
    }

    else
        my_printf(&huart1, "G: X=%d, Y=%d\r\n", coord.x, coord.y);
}

// 回调函数指针，默认指向默认回调函数
static LaserCoordCallback_t laser_coord_callback = default_laser_callback;

// 设置激光坐标回调函数
void maixcam_set_callback(LaserCoordCallback_t callback)
{
    if (callback != NULL)
        laser_coord_callback = callback;
    else
        laser_coord_callback = default_laser_callback;
}

// MaixCam 数据解析函数，支持格式：to:(X,Y) 和 pur:(X,Y)
int maixcam_parse_data(char *buffer)
{
    if (!buffer)
        return -1; // 缓冲区为空

    LaserCoord_t coord;
    
    // 检查是否为 to:(X,Y) 格式 (绿色激光)
    if (strncmp(buffer, "to:(", 4) == 0)
    {
        char *end_paren = strchr(buffer + 4, ')');
        if (!end_paren)
            return -2; // 格式错误，找不到结束括号
            
        // 解析坐标
        int parsed = sscanf(buffer + 4, "%d,%d", &coord.x, &coord.y);
        if (parsed != 2)
            return -3; // 坐标解析失败
            
        coord.type = GREEN_LASER_ID; // 设置为绿色激光
    }
    // 检查是否为 pur:(X,Y) 格式 (红色激光)
    else if (strncmp(buffer, "pur:(", 5) == 0)
    {
        char *end_paren = strchr(buffer + 5, ')');
        if (!end_paren)
            return -4; // 格式错误，找不到结束括号
            
        // 解析坐标
        int parsed = sscanf(buffer + 5, "%d,%d", &coord.x, &coord.y);
        if (parsed != 2)
            return -5; // 坐标解析失败
            
        coord.type = RED_LASER_ID; // 设置为红色激光
    }
    else
    {
        return -6; // 未知的数据格式
    }

    // 通过回调函数处理激光坐标
    if (laser_coord_callback)
        laser_coord_callback(coord);

    return 0; // 成功
}

// MaixCam 数据解析任务函数
void maixcam_task(MultiTimer *timer, void *userData)
{
    int length_cam = rt_ringbuffer_data_len(&ringbuffer_cam);
    if (length_cam > 0)
    {
        rt_ringbuffer_get(&ringbuffer_cam, output_buffer_cam, length_cam);
        output_buffer_cam[length_cam] = '\0';
        int result = maixcam_parse_data((char *)output_buffer_cam);
        memset(output_buffer_cam, 0, length_cam);
    }
    
//    multiTimerStart(&mt_pid, 10, app_pid_task, NULL);
}
